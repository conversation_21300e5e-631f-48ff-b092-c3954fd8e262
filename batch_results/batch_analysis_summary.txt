=== 批量累加器日志分析汇总报告 ===

分析时间: /Users/<USER>/Downloads/any
日志文件夹: logs
输出目录: batch_results

=== 分析结果 ===
总文件数: 5
成功: 5
失败: 0

=== 详细结果 ===
100000.accumulator.log: ✅ 成功 -> 100000.accumulator/
200000.accumulator.log: ✅ 成功 -> 200000.accumulator/
300000.accumulator.log: ✅ 成功 -> 300000.accumulator/
50000.accumulator.log: ✅ 成功 -> 50000.accumulator/
500000.accumulator.log: ✅ 成功 -> 500000.accumulator/

=== 生成的文件结构 ===
batch_results/
├── excel_reports/
│   ├── 100000.accumulator_feature_detailed_analysis.xlsx
│   ├── 200000.accumulator_feature_detailed_analysis.xlsx
│   ├── 300000.accumulator_feature_detailed_analysis.xlsx
│   ├── 50000.accumulator_feature_detailed_analysis.xlsx
│   ├── 500000.accumulator_feature_detailed_analysis.xlsx
└── batch_analysis_summary.txt

=== 说明 ===
- 所有Excel报告文件统一保存在 excel_reports/ 目录下
- Excel文件名格式: [日志文件名]_feature_detailed_analysis.xlsx
- 临时工作目录在分析完成后自动清理
