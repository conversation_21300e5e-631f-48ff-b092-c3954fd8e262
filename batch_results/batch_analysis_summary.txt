=== 批量累加器日志分析汇总报告 ===

分析时间: /Users/<USER>/Downloads/any
日志文件夹: logs
输出目录: batch_results

=== 分析结果 ===
总文件数: 5
成功: 5
失败: 0

=== 详细结果 ===
100000.accumulator.log: ✅ 成功 -> 100000.accumulator/
200000.accumulator.log: ✅ 成功 -> 200000.accumulator/
300000.accumulator.log: ✅ 成功 -> 300000.accumulator/
50000.accumulator.log: ✅ 成功 -> 50000.accumulator/
500000.accumulator.log: ✅ 成功 -> 500000.accumulator/

=== 生成的文件结构 ===
batch_results/
├── 100000.accumulator/
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   └── log_processor.log
├── 200000.accumulator/
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   └── log_processor.log
├── 300000.accumulator/
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   └── log_processor.log
├── 50000.accumulator/
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   └── log_processor.log
├── 500000.accumulator/
│   ├── accumulator.log
│   ├── accumulator_data.csv
│   ├── feature_detailed_analysis.xlsx
│   ├── analysis_summary.txt
│   ├── charts/
│   └── log_processor.log
