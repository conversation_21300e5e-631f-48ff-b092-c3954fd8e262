#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量累加器日志分析工具

功能：
1. 指定日志文件夹，分析文件夹里的每一个日志文件
2. 为每个日志文件生成独立的分析结果
3. 结果保存到以日志文件名命名的子目录中

使用方法：
python batch_analyzer.py <日志文件夹路径>

作者：AI Assistant
日期：2025-06-19
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import argparse


class BatchAnalyzer:
    """批量分析器类"""
    
    def __init__(self, log_folder: str, output_base_dir: str = "batch_results"):
        """
        初始化批量分析器
        
        Args:
            log_folder: 日志文件夹路径
            output_base_dir: 输出基础目录
        """
        self.log_folder = Path(log_folder)
        self.output_base_dir = Path(output_base_dir)
        self.current_dir = Path.cwd()
        
        # 检查日志文件夹是否存在
        if not self.log_folder.exists():
            raise FileNotFoundError(f"日志文件夹不存在: {self.log_folder}")
            
        # 创建输出基础目录
        self.output_base_dir.mkdir(exist_ok=True)
        
    def find_log_files(self):
        """查找日志文件夹中的所有日志文件"""
        log_files = []
        
        # 支持的日志文件扩展名
        log_extensions = ['.log', '.txt']
        
        for file_path in self.log_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in log_extensions:
                log_files.append(file_path)
                
        return sorted(log_files)
        
    def get_output_dir_name(self, log_file: Path):
        """根据日志文件名生成输出目录名"""
        # 移除扩展名，使用文件名作为目录名
        return log_file.stem
        
    def copy_scripts_to_output_dir(self, output_dir: Path):
        """复制必要的脚本到输出目录"""
        scripts_to_copy = [
            'log_processor.py',
            'simple_analyzer.py'
        ]
        
        for script in scripts_to_copy:
            script_path = self.current_dir / script
            if script_path.exists():
                shutil.copy2(script_path, output_dir)
            else:
                raise FileNotFoundError(f"必要脚本不存在: {script}")
                
    def run_analysis_for_file(self, log_file: Path):
        """为单个日志文件运行分析"""
        print(f"\n{'='*60}")
        print(f"🔄 分析日志文件: {log_file.name}")
        print(f"{'='*60}")
        
        # 创建输出目录
        output_dir_name = self.get_output_dir_name(log_file)
        output_dir = self.output_base_dir / output_dir_name
        output_dir.mkdir(exist_ok=True)
        
        print(f"📁 输出目录: {output_dir}")
        
        # 复制日志文件到输出目录，重命名为accumulator.log
        target_log_file = output_dir / "accumulator.log"
        shutil.copy2(log_file, target_log_file)
        print(f"📋 复制日志文件: {log_file} -> {target_log_file}")
        
        # 复制脚本到输出目录
        self.copy_scripts_to_output_dir(output_dir)
        print(f"📜 复制分析脚本到输出目录")
        
        # 切换到输出目录运行分析
        original_cwd = os.getcwd()
        try:
            os.chdir(output_dir)
            
            # 步骤1: 处理日志文件
            print(f"\n🔄 步骤1: 处理日志文件...")
            result1 = subprocess.run(
                [sys.executable, "log_processor.py"],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result1.returncode != 0:
                print(f"❌ 日志处理失败:")
                print(result1.stderr)
                return False
            else:
                print(f"✅ 日志处理完成")
                # 显示处理结果的关键信息
                if "总记录数:" in result1.stdout:
                    lines = result1.stdout.split('\n')
                    for line in lines:
                        if any(keyword in line for keyword in ["总记录数:", "特征类型分布:", "内存可用性:"]):
                            print(f"   {line.strip()}")
            
            # 步骤2: 生成Excel报告和图表
            print(f"\n🔄 步骤2: 生成Excel报告和图表...")
            result2 = subprocess.run(
                [sys.executable, "simple_analyzer.py"],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result2.returncode != 0:
                print(f"❌ 分析失败:")
                print(result2.stderr)
                return False
            else:
                print(f"✅ 分析完成")
                # 显示分析结果的关键信息
                if "有效内存数据:" in result2.stdout:
                    lines = result2.stdout.split('\n')
                    for line in lines:
                        if any(keyword in line for keyword in ["有效内存数据:", "✅ DISTRIBUTION:", "✅ RATIO:", "✅ SEGMENT:"]):
                            print(f"   {line.strip()}")
            
            # 清理脚本文件（可选）
            for script in ['log_processor.py', 'simple_analyzer.py']:
                script_path = Path(script)
                if script_path.exists():
                    script_path.unlink()
            
            print(f"🎉 {log_file.name} 分析完成！")
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中出错: {e}")
            return False
        finally:
            os.chdir(original_cwd)
            
    def generate_summary_report(self, results):
        """生成批量分析汇总报告"""
        summary_file = self.output_base_dir / "batch_analysis_summary.txt"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=== 批量累加器日志分析汇总报告 ===\n\n")
            f.write(f"分析时间: {Path().cwd()}\n")
            f.write(f"日志文件夹: {self.log_folder}\n")
            f.write(f"输出目录: {self.output_base_dir}\n\n")
            
            f.write(f"=== 分析结果 ===\n")
            f.write(f"总文件数: {len(results)}\n")
            successful = sum(1 for success in results.values() if success)
            failed = len(results) - successful
            f.write(f"成功: {successful}\n")
            f.write(f"失败: {failed}\n\n")
            
            f.write(f"=== 详细结果 ===\n")
            for log_file, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                output_dir = self.get_output_dir_name(Path(log_file))
                f.write(f"{log_file}: {status} -> {output_dir}/\n")
                
            f.write(f"\n=== 生成的文件结构 ===\n")
            f.write(f"{self.output_base_dir}/\n")
            for log_file, success in results.items():
                if success:
                    output_dir = self.get_output_dir_name(Path(log_file))
                    f.write(f"├── {output_dir}/\n")
                    f.write(f"│   ├── accumulator.log\n")
                    f.write(f"│   ├── accumulator_data.csv\n")
                    f.write(f"│   ├── feature_detailed_analysis.xlsx\n")
                    f.write(f"│   ├── analysis_summary.txt\n")
                    f.write(f"│   ├── charts/\n")
                    f.write(f"│   └── log_processor.log\n")
                    
        print(f"\n📋 汇总报告已生成: {summary_file}")
        
    def run_batch_analysis(self):
        """运行批量分析"""
        print(f"🚀 批量累加器日志分析工具")
        print(f"📁 日志文件夹: {self.log_folder}")
        print(f"📁 输出目录: {self.output_base_dir}")
        
        # 查找日志文件
        log_files = self.find_log_files()
        
        if not log_files:
            print(f"❌ 在文件夹 {self.log_folder} 中未找到日志文件")
            print(f"支持的文件扩展名: .log, .txt")
            return
            
        print(f"\n📋 找到 {len(log_files)} 个日志文件:")
        for i, log_file in enumerate(log_files, 1):
            print(f"  {i}. {log_file.name}")
            
        # 确认是否继续
        response = input(f"\n是否继续分析这些文件? (y/n): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("❌ 用户取消操作")
            return
            
        # 批量分析
        results = {}
        for i, log_file in enumerate(log_files, 1):
            print(f"\n进度: {i}/{len(log_files)}")
            success = self.run_analysis_for_file(log_file)
            results[log_file.name] = success
            
        # 生成汇总报告
        self.generate_summary_report(results)
        
        # 显示最终结果
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        print(f"\n🎉 批量分析完成！")
        print(f"📊 总计: {len(log_files)} 个文件")
        print(f"✅ 成功: {successful} 个")
        print(f"❌ 失败: {failed} 个")
        print(f"📁 结果保存在: {self.output_base_dir}/")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量累加器日志分析工具')
    parser.add_argument('log_folder', help='日志文件夹路径')
    parser.add_argument('-o', '--output', default='batch_results', help='输出基础目录 (默认: batch_results)')
    
    args = parser.parse_args()
    
    try:
        analyzer = BatchAnalyzer(args.log_folder, args.output)
        analyzer.run_batch_analysis()
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
