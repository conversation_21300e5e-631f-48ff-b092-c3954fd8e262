#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键运行简化版累加器分析工具

功能：
1. 处理日志文件生成CSV
2. 生成Excel报告和图表
3. 只分析有效内存数据

作者：AI Assistant
日期：2025-06-19
"""

import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """运行命令"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout.strip():
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def check_files():
    """检查必要文件"""
    required_files = ["accumulator.log", "log_processor.py", "simple_analyzer.py"]
    missing = [f for f in required_files if not Path(f).exists()]
    
    if missing:
        print(f"❌ 缺少文件: {', '.join(missing)}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True


def main():
    """主函数"""
    print("🚀 简化版累加器分析工具")
    print("=" * 40)
    
    # 检查文件
    if not check_files():
        sys.exit(1)
    
    # 步骤1: 处理日志
    if not run_command("python log_processor.py", "处理日志文件"):
        sys.exit(1)
    
    # 步骤2: 生成Excel和图表
    if not run_command("python simple_analyzer.py", "生成Excel报告和图表"):
        sys.exit(1)
    
    print("\n🎉 分析完成！")
    print("生成的文件:")
    print("📊 feature_detailed_analysis.xlsx - Excel详细报告")
    print("   包含: 总览、全部数据、各类型详情工作表")
    print("📈 charts/ - 图表目录")
    print("📝 analysis_summary.txt - 分析摘要")


if __name__ == "__main__":
    main()
